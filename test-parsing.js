// Simple test to check markdown parsing
import { parseEnhancedMarkdownResume } from './src/services/markdownDataService.ts';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testParsing() {
  try {
    console.log('Starting to load markdown resume...');
    const markdownPath = join(__dirname, 'public', 'updatedresume.md');
    const markdownContent = readFileSync(markdownPath, 'utf-8');
    console.log('Markdown file loaded, parsing...');
    const data = await parseEnhancedMarkdownResume(markdownContent);
    console.log('Resume data parsed successfully');
    console.log('Number of work entries:', data.work.length);
    console.log('Work entries:');
    data.work.forEach((entry, index) => {
      console.log(`${index + 1}. ${entry.name} - ${entry.position}`);
    });
  } catch (error) {
    console.error('Error loading resume:', error);
  }
}

testParsing();