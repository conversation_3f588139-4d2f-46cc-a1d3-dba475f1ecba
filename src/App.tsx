/**
 * @file App.tsx
 * @description This is the main entry point for the application.
 * It sets up the routing, providers, and other global components.
 * @requires react
 * @requires react-router-dom
 * @requires @tanstack/react-query
 * @requires @/components/ui/toaster
 * @requires @/components/ui/sonner
 * @requires @/components/ui/tooltip
 * @requires @stagewise/toolbar-react
 * @requires @stagewise-plugins/react
 */

import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { lazy, Suspense } from "react";
const Index = lazy(() => import("./pages/Index"));
const NotFound = lazy(() => import("./pages/NotFound"));

/**
 * @description The StagewiseToolbar is a development tool that is only available in development mode.
 * @see https://stagewise.dev/docs/toolbar/react
 */
import { StagewiseToolbar } from "@stagewise/toolbar-react";
import { ReactPlugin } from "@stagewise-plugins/react";

const queryClient = new QueryClient();

/**
 * @function App
 * @description The main application component.
 * @returns {JSX.Element}
 */
const App = () => {
  const isDevelopment = import.meta.env.DEV;

  return (
    <>
      {isDevelopment && (
        <StagewiseToolbar
          config={{
            plugins: [ReactPlugin],
          }}
        />
      )}
      {/* @description The QueryClientProvider is used to provide a QueryClient to all child components. */}
      {/* @see https://tanstack.com/query/v4/docs/react/reference/QueryClientProvider */}
      <QueryClientProvider client={queryClient}>
        {/* @description The TooltipProvider is used to provide a Tooltip to all child components. */}
        <TooltipProvider>
          {/* @description The Toaster component is used to display toast notifications. */}
          <Toaster />
          {/* @description The Sonner component is used to display toast notifications. */}
          <Sonner />
          {/* @description The BrowserRouter component is used to provide routing to the application. */}
          {/* @see https://reactrouter.com/web/guides/quick-start */}
          <BrowserRouter>
            {/* @description The Suspense component is used to display a fallback while the routes are being loaded. */}
            {/* @see https://reactjs.org/docs/code-splitting.html#reactlazy */}
            <Suspense fallback={<div>Loading...</div>}>
              {/* @description The Routes component is used to define the routes for the application. */}
              <Routes>
                <Route path="/" element={<Index />} />
                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Suspense>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </>
  );
};

export default App;
