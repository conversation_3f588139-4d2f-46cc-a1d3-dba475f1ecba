/**
 * @file HeroSection.tsx
 * @description This component displays the hero section of the resume, including the name, title, summary, and profile image.
 * @requires react
 */

import React from "react";
// import { Card } from "@/components/ui/card"; // Unused
// import { Button } from "@/components/ui/button"; // Unused
// import { Linkedin, Mail } from "lucide-react"; // Unused

interface HeroSectionProps {
  resumeData: {
    basics: {
      name: string;
      label: string;
      summary: string;
      location: string;
      email?: string;
      profiles: {
        linkedin: string;
        github?: string;
      };
    };
  };
}

/**
 * @function HeroSection
 * @description React component for the hero section of the resume.
 * @param {HeroSectionProps} { resumeData } - Props containing the resume data.
 * @returns {JSX.Element}
 */
const HeroSection = ({ resumeData }: HeroSectionProps) => {
  return (
    <section className="py-16 px-6">
      <div className="container mx-auto max-w-6xl">
        <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-12 mb-12">
          {/* Text Content */}
          <div className="flex-1 order-2 lg:order-1">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              <div>Principal Technical Consultant</div>
              <div className="text-blue-600">Cloud and Platform Engineering</div>
            </h2>
            <p className="text-sm text-gray-600 max-w-4xl leading-normal">
              {resumeData.basics.summary}
            </p>
          </div>
          
          {/* Profile Image */}
          <div className="flex-shrink-0 order-1 lg:order-2">
            <img
              src="/selfie.png"
              alt="Nicholas Gerasimatos"
              className="w-56 h-56 md:w-64 md:h-64 lg:w-72 lg:h-72 rounded-full object-cover shadow-lg border-4 border-white"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
