/**
 * @file PublicationsSection.tsx
 * @description This component displays the publications and awards section of the resume.
 * It renders a list of awards and publications.
 * @requires react
 * @requires @/components/ui/card
 * @requires @/components/ui/button
 * @requires @/components/ui/separator
 * @requires lucide-react
 */

import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Star, BookOpen, ExternalLink } from "lucide-react";

interface Award {
  title: string;
  date: string;
  awarder: string;
  summary: string;
  url?: string;
}

interface Publication {
  name: string;
  publisher: string;
  releaseDate: string;
  url?: string;
}

/**
 * @interface PublicationsSectionProps
 * @description Props for the PublicationsSection component.
 * @property {Award[]} awards - An array of award objects.
 * @property {Publication[]} publications - An array of publication objects.
 */
interface PublicationsSectionProps {
  awards: Award[];
  publications: Publication[];
}

/**
 * @function PublicationsSection
 * @description React component to display publications and awards.
 * @param {PublicationsSectionProps} { awards, publications } - Props containing the awards and publications data.
 * @returns {JSX.Element}
 */
const PublicationsSection = ({
  awards,
  publications,
}: PublicationsSectionProps) => {
  // const formatDate = (dateString: string) => { // Unused function
  //   const date = new Date(dateString);
  //   return date.toLocaleDateString("en-US", {
  //     month: "short",
  //     year: "numeric",
  //   });
  // };

  return (
    <div className="space-y-8 animate-in fade-in duration-500">
      <h3 className="text-3xl font-bold text-center mb-12">
        Publications & Thought Leadership
      </h3>

      {/* Awards */}
      <div className="mb-12">
        <h4 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Star className="h-6 w-6 text-yellow-500" />
          Awards & Recognition
        </h4>
        {awards.map((award, index) => (
          <Card
            key={index}
            className="p-8 border-l-4 border-l-yellow-500 bg-gradient-to-r from-yellow-50 to-white"
          >
            <div className="flex items-start gap-4">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <Star className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="flex-1">
                <h5 className="text-lg font-semibold text-gray-900 mb-2">
                  {award.title}
                </h5>
                <div className="text-sm text-gray-600 mb-4">
                  <span className="font-medium text-yellow-600">
                    {award.awarder}
                  </span>
                </div>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {award.summary}
                </p>
                {award.url && (
                  <Button
                    variant="link"
                    size="sm"
                    className="px-0 h-auto"
                    asChild
                  >
                    <a
                      href={award.url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Read More <ExternalLink className="h-4 w-4 ml-1" />
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>

      <Separator />

      {/* Publications */}
      <div>
        <h4 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <BookOpen className="h-6 w-6 text-blue-600" />
          Featured Publications
        </h4>
        <div className="grid gap-6">
          {publications.map((pub, index) => (
            <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start gap-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BookOpen className="h-6 w-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h5 className="font-semibold text-gray-900 mb-2">
                    {pub.name}
                  </h5>
                  <div className="text-sm text-gray-600 mb-2">
                    <span className="font-medium text-blue-600">
                      {pub.publisher}
                    </span>
                  </div>
                  {pub.url && (
                    <Button
                      variant="link"
                      size="sm"
                      className="px-0 h-auto mt-2"
                      asChild
                    >
                      <a
                        href={pub.url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Read Article <ExternalLink className="h-4 w-4 ml-1" />
                      </a>
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PublicationsSection;
