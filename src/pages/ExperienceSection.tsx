/**
 * @file ExperienceSection.tsx
 * @description This component displays the professional work experience section of the resume.
 * It maps through an array of job experiences and renders each as a card.
 * @requires react
 * @requires @/components/ui/card
 * @requires @/components/ui/badge
 * @requires lucide-react (commented out unused import)
 */

import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
// import { Calendar } from "lucide-react"; // Unused import

interface Job {
  name: string;
  position: string;
  startDate?: string;
  endDate?: string;
  current?: boolean;
  highlights: string[];
  technologies: string[];
  logo?: string;
  url?: string;
}

/**
 * @interface ExperienceSectionProps
 * @description Props for the ExperienceSection component.
 * @property {Job[]} work - An array of job experience objects.
 */
interface ExperienceSectionProps {
  work: Job[];
}

/**
 * @constant COMPANY_LOGO_MAP
 * @description Mapping of company names to their respective logo paths
 */
const COMPANY_LOGO_MAP: { [key: string]: string } = {
  AHEAD: "https://www.netapp.com/media/Ahead_tcm19-56900.png?v=5358",
  "Amazon Web Services (AWS)": "/logos/amazonwebservices.svg",
  "Amazon Web Services": "/logos/amazonwebservices.svg",
  "Red Hat": "/logos/redhat.png", 
  FICO: "/logos/fico.svg",
  "American Express": "/logos/americanexpress.png",
  VCE: "https://www.logo.wine/a/logo/VCE_(company)/VCE_(company)-Logo.wine.svg",
  Microsoft:
    "https://upload.wikimedia.org/wikipedia/commons/9/96/Microsoft_logo_%282012%29.svg",
};

/**
 * @function ExperienceSection
 * @description React component to display professional work experience.
 * @param {ExperienceSectionProps} { work } - Props containing the work experience data.
 * @returns {JSX.Element}
 */
const ExperienceSection = ({ work }: ExperienceSectionProps) => {
  /**
   * @function LogoImage
   * @description Enhanced image component with loading state and error handling
   * @param {Object} props - Component props
   * @param {string} props.src - Image source URL
   * @param {string} props.alt - Alt text for accessibility
   * @param {string} props.companyName - Company name for fallback logo lookup
   * @returns {JSX.Element}
   */
  const LogoImage = ({ src, alt, companyName }: { src: string; alt: string; companyName: string }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [imageError, setImageError] = useState(false);
    
    // Use the logo from the map, fallback to passed src, then to placeholder
    const logoSrc = COMPANY_LOGO_MAP[companyName] || src || "/placeholder.svg";
    


    const handleImageLoad = () => {
      setIsLoading(false);
    };

    const handleImageError = () => {
      setImageError(true);
      setIsLoading(false);
    };

    return (
      <>
        {isLoading && (
          <div className="w-10 h-10 bg-gray-200 animate-pulse rounded" />
        )}
        <img
          src={imageError ? "/placeholder.svg" : logoSrc}
          alt={alt}
          className={`w-10 h-10 object-contain ${isLoading ? 'hidden' : ''}`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading="lazy"
        />
      </>
    );
  };

  /**
   * @function getLogoPath
   * @description Helper function to get the correct logo path based on company name.
   * @param {string} companyName - The name of the company.
   * @returns {string} The URL of the company logo or a placeholder if not found.
   */
  const getLogoPath = (companyName: string): string => {
    return COMPANY_LOGO_MAP[companyName] || "/placeholder.svg";
  };

  return (
    <div className="space-y-8 animate-in fade-in duration-500">
      <h3 className="text-3xl font-bold text-center mb-12">
        Professional Experience
      </h3>
      {work.map((job, index) => (
        <Card key={index} className="p-8 hover:shadow-lg transition-shadow">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-6">
            <div className="flex items-start gap-4 mb-4 md:mb-0">
              <div className="flex-shrink-0 w-12 h-12 bg-white rounded-lg border border-gray-200 flex items-center justify-center p-1">
                <LogoImage
                  src={job.logo || getLogoPath(job.name)}
                  alt={`${job.name} logo`}
                  companyName={job.name}
                />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h4 className="text-xl font-semibold text-gray-900">
                    {job.position.replace(/\([^)]*\)/g, "").trim()}
                    {job.current && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Current
                      </span>
                    )}
                  </h4>
                </div>
                <h5 className="text-lg font-medium text-blue-600 mb-2">
                  {job.url ? (
                    <a
                      href={job.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:underline"
                    >
                      {job.name}
                    </a>
                  ) : (
                    job.name
                  )}
                </h5>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <ul className="space-y-2">
              {job.highlights.map((highlight, i) => (
                <li key={i} className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-700 leading-relaxed">
                    {highlight}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex flex-wrap gap-2">
            {job.technologies.map((tech, i) => (
              <Badge
                key={i}
                variant="secondary"
                className="bg-blue-50 text-blue-700 hover:bg-blue-100"
              >
                {tech}
              </Badge>
            ))}
          </div>
        </Card>
      ))}
    </div>
  );
};

export default ExperienceSection;
