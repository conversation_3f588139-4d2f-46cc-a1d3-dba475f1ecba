/**
 * @file Index.tsx
 * @description This is the main page component that orchestrates the display of various resume sections.
 * It handles data loading, error states, and dynamic section rendering based on user interaction.
 * @requires react
 * @requires @/components/ui/button
 * @requires @/components/ui/alert
 * @requires @/components/ResumeDownload
 * @requires @/hooks/useMarkdownResume
 * @requires lucide-react
 * @requires ./NavigationTabs
 * @requires ./HeroSection (lazy loaded)
 * @requires ./ExperienceSection (lazy loaded)
 * @requires ./SkillsSection (lazy loaded)
 * @requires ./PublicationsSection (lazy loaded)
 * @requires @/components/FeaturedProjects (lazy loaded)
 */

import React, { useState, lazy, Suspense } from "react";
// import Image from "next/image"; // Unused, and this is not a Next.js project
import { Button } from "@/components/ui/button";
import { /* Mail, */ Linkedin, Github, AlertCircle, Loader2 } from "lucide-react"; // Mail is unused
import { Alert, AlertDescription } from "@/components/ui/alert";
const FeaturedProjects = lazy(() => import("@/components/FeaturedProjects"));
const HeroSection = lazy(() => import("./HeroSection"));
const ExperienceSection = lazy(() => import("./ExperienceSection"));
const SkillsSection = lazy(() => import("./SkillsSection"));
const PublicationsSection = lazy(() => import("./PublicationsSection"));
import NavigationTabs from "./NavigationTabs";
import ResumeDownload from "@/components/ResumeDownload";
import { useResumeData } from "@/hooks/useMarkdownResume";

const Index = () => {
  const [activeSection, setActiveSection] = useState("experience");
  const { data: resumeData, loading, error, refetch } = useResumeData();

  /**
   * @description Displays a loading spinner while resume data is being fetched.
   */
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading resume data...</p>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* @section Error Alert */}
      {/* @description Displays an alert if there's an error loading resume data, offering a retry option. */}
      {error && (
        <Alert className="mx-6 mt-4 border-orange-200 bg-orange-50">
          <AlertCircle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            Failed to load resume data from markdown file. Using fallback data.
            <Button
              variant="link"
              size="sm"
              onClick={refetch}
              className="ml-2 h-auto p-0 text-orange-600 underline"
            >
              Try again
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* @section Header */}
      {/* @description The main header of the resume page, containing name, label, and social links. */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {resumeData.basics.name}
              </h1>
              <p className="text-gray-600">{resumeData.basics.label}</p>
            </div>
            <div className="flex items-center gap-4">
              <ResumeDownload resumeData={resumeData} />
              <Button variant="outline" size="sm" asChild>
                <a
                  href="https://github.com/nicholasg-dev"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Github className="h-4 w-4 mr-2" />
                  GitHub
                </a>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <a
                  href={resumeData.basics.profiles.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Linkedin className="h-4 w-4 mr-2" />
                  LinkedIn
                </a>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* @section Hero Section */}
      {/* @description Displays the Hero Section, lazy-loaded for performance. */}
      <Suspense fallback={<div>Loading...</div>}>
        <HeroSection resumeData={resumeData} />
      </Suspense>

      {/* @section Navigation Tabs */}
      {/* @description Provides navigation tabs to switch between different resume sections. */}
      <section className="pb-16 px-6">
        <div className="container mx-auto max-w-6xl">
          <NavigationTabs
            activeSection={activeSection}
            setActiveSection={setActiveSection}
          />
        </div>
      </section>

      {/* @section Content Sections */}
      {/* @description Dynamically renders the active resume section, lazy-loaded for performance. */}
      <section className="pb-16 px-6">
        <div className="container mx-auto max-w-6xl">
          <Suspense fallback={<div>Loading...</div>}>
            {activeSection === "experience" && (
              <ExperienceSection work={resumeData.work} />
            )}
            {activeSection === "skills" && (
              <SkillsSection skills={resumeData.skills} />
            )}
            {activeSection === "projects" && (
              <div className="animate-in fade-in duration-500">
                <FeaturedProjects />
              </div>
            )}
            {activeSection === "publications" && (
              <PublicationsSection
                awards={resumeData.awards}
                publications={resumeData.publications}
              />
            )}
          </Suspense>
        </div>
      </section>

      {/* @section Footer */}
      {/* @description The footer section of the resume page, including contact call-to-action and copyright. */}
      <footer className="bg-gray-900 text-white py-12 px-6">
        <div className="container mx-auto max-w-6xl text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Connect?</h3>
          <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
            I'm always interested in discussing innovative cloud solutions,
            emerging technologies, and opportunities to drive digital
            transformation.
          </p>
          <div className="flex justify-center">
            <Button asChild size="lg">
              <a
                href={resumeData.basics.profiles.linkedin}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Linkedin className="h-5 w-5 mr-2" />
                Connect on LinkedIn
              </a>
            </Button>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-800 text-gray-500 text-sm">
            <p>
              © 2024 {resumeData.basics.name}. Built with modern web
              technologies.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
