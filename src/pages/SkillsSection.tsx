/**
 * @file SkillsSection.tsx
 * @description This component displays the technical skills section of the resume.
 * It categorizes skills into groups and renders them as cards.
 * @requires react
 * @requires @/components/ui/card
 * @requires @/components/ui/badge
 */

import React from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface SkillGroup {
  name: string;
  keywords: string[];
}

/**
 * @interface SkillsSectionProps
 * @description Props for the SkillsSection component.
 * @property {SkillGroup[]} skills - An array of skill group objects.
 */
interface SkillsSectionProps {
  skills: SkillGroup[];
}

/**
 * @function SkillsSection
 * @description React component to display technical skills.
 * @param {SkillsSectionProps} { skills } - Props containing the skills data.
 * @returns {JSX.Element}
 */
const SkillsSection = ({ skills }: SkillsSectionProps) => {
  return (
    <div className="space-y-8 animate-in fade-in duration-500">
      <h3 className="text-3xl font-bold text-center mb-12">Technical Skills</h3>
      <div className="grid gap-8">
        {skills.map((skillGroup, index) => (
          <Card key={index} className="p-8">
            <h4 className="text-xl font-semibold mb-6 text-gray-900">
              {skillGroup.name}
            </h4>
            <div className="flex flex-wrap gap-2">
              {skillGroup.keywords.map((skill, i) => (
                <Badge
                  key={i}
                  variant="outline"
                  className="px-3 py-1.5 hover:bg-blue-50 hover:border-blue-200 transition-colors"
                >
                  {skill}
                </Badge>
              ))}
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default SkillsSection;
