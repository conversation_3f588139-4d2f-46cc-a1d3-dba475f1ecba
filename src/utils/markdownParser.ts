/**
 * @file markdownParser.ts
 * @description This utility provides functions to parse markdown content into a structured resume data object.
 * It leverages `marked` for markdown tokenization and `gray-matter` for front matter parsing.
 * @requires marked
 * @requires gray-matter
 */

import { marked } from 'marked';
import matter from 'gray-matter';

export interface ParsedMarkdownData {
  basics: {
    name: string;
    label: string;
    summary: string;
    location: string;
    email?: string;
    profiles: {
      linkedin: string;
      github?: string;
    };
  };
  work: Array<{
    name: string;
    position: string;
    startDate?: string;
    endDate?: string;
    current?: boolean;
    highlights: string[];
    technologies: string[];
    summary?: string;
    url?: string;
  }>;
  skills: Array<{
    name: string;
    keywords: string[];
  }>;
  publications: Array<{
    name: string;
    publisher: string;
    releaseDate: string;
    url?: string;
  }>;
  awards: Array<{
    title: string;
    date: string;
    awarder: string;
    summary: string;
    url?: string;
  }>;
  certificates: Array<{
    name: string;
    issuer: string;
    startDate?: string;
    endDate?: string;
    url?: string;
  }>;
  volunteer?: Array<{
    organization: string;
    position: string;
    startDate?: string;
    current?: boolean;
    summary: string;
    highlights: string[];
  }>;
  references?: Array<{
    name: string;
    reference: string;
  }>;
}

/**
 * @function parseMarkdownResume
 * @description Parses markdown content and extracts structured resume data.
 * It uses `marked.lexer` to tokenize the markdown and then processes these tokens
 * to populate the `ParsedMarkdownData` object.
 * @param {string} markdownContent - The raw markdown content of the resume.
 * @returns {Promise<ParsedMarkdownData>} A promise that resolves with the parsed resume data.
 */
export async function parseMarkdownResume(markdownContent: string): Promise<ParsedMarkdownData> {
  // Parse front matter if it exists
  const { content, data: frontMatter } = matter(markdownContent);
  
  // Convert markdown to HTML tokens for easier parsing
  const tokens = marked.lexer(content);
  
  // Initialize the result object
  const result: ParsedMarkdownData = {
    basics: {
      name: '',
      label: '',
      summary: '',
      location: 'United States',
      profiles: {
        linkedin: '',
        github: ''
      }
    },
    work: [],
    skills: [],
    publications: [],
    awards: [],
    certificates: [],
    volunteer: [],
    references: []
  };

  let currentSection = '';
  let currentWorkEntry: any = null;
  let currentSkillCategory = '';
  let currentPublicationData: any = {};
  let isInTable = false;
  let tableHeaders: string[] = [];
  let tableRowData: string[] = [];

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i];

    if (token.type === 'heading') {
      const text = token.text.toLowerCase();
      
      // Extract name from the main heading
      if (token.depth === 1) {
        result.basics.name = token.text;
        continue;
      }
      
      // Extract label/title from subtitle
      if (token.depth === 2 && !currentSection) {
        result.basics.label = token.text;
        continue;
      }
      
      // Section headers
      if (token.depth === 2) {
        currentSection = text;
        currentWorkEntry = null;
        currentSkillCategory = '';
        isInTable = false;
        continue;
      }
      
      // Work experience entries
      if (currentSection === 'work experience' && token.depth === 3) {
        if (currentWorkEntry) {
          result.work.push(currentWorkEntry);
        }
        
        // Parse company name and position
        const parts = token.text.split('—').map(p => p.trim());
        currentWorkEntry = {
          name: parts[0] || '',
          position: parts[1] || '',
          highlights: [],
          technologies: [],
          current: false
        };
        continue;
      }
      
      // Date ranges for work experience
      if (currentSection === 'work experience' && token.depth === 4 && currentWorkEntry) {
        const dateText = token.text;
        if (dateText.toLowerCase().includes('present')) {
          currentWorkEntry.current = true;
          currentWorkEntry.endDate = 'Present';
        }
        // Extract start date from date range
        const dateMatch = dateText.match(/(\w+\s+\d{4})/);
        if (dateMatch) {
          currentWorkEntry.startDate = dateMatch[1];
        }
        continue;
      }
    }

    if (token.type === 'paragraph') {
      const text = token.text;
      
      // Extract GitHub and LinkedIn from early paragraphs
      if (!currentSection) {
        if (text.includes('github.com')) {
          const githubMatch = text.match(/github\.com\/([^)\s]+)/);
          if (githubMatch) {
            result.basics.profiles.github = `https://github.com/${githubMatch[1]}`;
          }
        }
        if (text.includes('linkedin.com')) {
          const linkedinMatch = text.match(/linkedin\.com\/in\/([^)\s]+)/);
          if (linkedinMatch) {
            result.basics.profiles.linkedin = `https://linkedin.com/in/${linkedinMatch[1]}`;
          }
        }
        continue;
      }
      
      // Professional summary
      if (currentSection === 'professional summary') {
        result.basics.summary = text;
        continue;
      }
    }

    if (token.type === 'list') {
      // Work experience highlights
      if (currentSection === 'work experience' && currentWorkEntry) {
        token.items.forEach((item: any) => {
          const text = item.text;
          if (text.startsWith('**') && text.includes(':**')) {
            // This is a highlight point
            currentWorkEntry.highlights.push(text.replace(/\*\*/g, ''));
          }
        });
        continue;
      }
    }

    if (token.type === 'table') {
      isInTable = true;
      tableHeaders = token.header.map((h: any) => h.text.toLowerCase());
      
      // Handle publications table
      if (currentSection === 'publications') {
        token.rows.forEach((row: any) => {
          const publication = {
            name: '',
            publisher: '',
            releaseDate: '',
            url: ''
          };
          
          row.forEach((cell: any, index: number) => {
            const header = tableHeaders[index];
            const cellText = cell.text;
            
            if (header === 'title') {
              publication.name = cellText;
            } else if (header === 'publisher') {
              publication.publisher = cellText;
            } else if (header === 'url') {
              // Extract URL from markdown link
              const urlMatch = cellText.match(/\[.*?\]\((.*?)\)/);
              publication.url = urlMatch ? urlMatch[1] : cellText;
            }
          });
          
          if (publication.name && publication.publisher) {
            result.publications.push(publication);
          }
        });
      }
      
      // Handle key projects table
      if (currentSection === 'key projects') {
        token.rows.forEach((row: any) => {
          // This could be used for featured projects if needed
        });
      }
      
      continue;
    }
  }

  // Add the last work entry if it exists
  if (currentWorkEntry) {
    result.work.push(currentWorkEntry);
  }

  // Parse skills section separately due to its complex structure
  await parseSkillsSection(content, result);
  
  // Parse certifications section
  await parseCertificationsSection(content, result);
  
  // Parse awards section
  await parseAwardsSection(content, result);

  return result;
}

/**
 * Parse the skills section which has a complex nested structure
 */
async function parseSkillsSection(content: string, result: ParsedMarkdownData): Promise<void> {
  const skillsMatch = content.match(/Technical Skills\s*\n-+\s*\n([\s\S]*?)(?=\n[A-Z][^:\n]*\n-+|$)/i);
  if (!skillsMatch) return;

  const skillsContent = skillsMatch[1];
  const skillCategories = skillsContent.split(/\*\*([^:]+):\*\*/);
  
  for (let i = 1; i < skillCategories.length; i += 2) {
    const categoryName = skillCategories[i].trim();
    const skillsText = skillCategories[i + 1];
    
    if (skillsText) {
      // Extract skills from the text, handling various separators
      const skills = skillsText
        .split(/[,;]/)
        .map(skill => skill.trim())
        .filter(skill => skill.length > 0)
        .map(skill => skill.replace(/^\s*[\-\*]\s*/, '')) // Remove bullet points
        .filter(skill => skill.length > 0);
      
      if (skills.length > 0) {
        result.skills.push({
          name: categoryName,
          keywords: skills
        });
      }
    }
  }
}

/**
 * Parse the certifications section
 */
async function parseCertificationsSection(content: string, result: ParsedMarkdownData): Promise<void> {
  const certsMatch = content.match(/Certifications\s*\n-+\s*\n([\s\S]*?)(?=\n[A-Z][^:\n]*\n-+|$)/i);
  if (!certsMatch) return;

  const certsContent = certsMatch[1];
  
  // Parse certification entries
  const certLines = certsContent.split('\n').filter(line => line.trim().startsWith('*'));
  
  certLines.forEach(line => {
    // Extract certification name and issuer
    const match = line.match(/\*\s*\[([^\]]+)\]\([^)]+\)\s*–\s*([^,]+)(?:,\s*issued\s*(.+))?/i);
    if (match) {
      const cert = {
        name: match[1],
        issuer: match[2],
        startDate: match[3] || '',
        url: ''
      };
      
      // Extract URL
      const urlMatch = line.match(/\]\(([^)]+)\)/);
      if (urlMatch) {
        cert.url = urlMatch[1];
      }
      
      result.certificates.push(cert);
    } else {
      // Handle simpler format without links
      const simpleMatch = line.match(/\*\s*([^–]+)(?:–\s*(.+))?/);
      if (simpleMatch) {
        result.certificates.push({
          name: simpleMatch[1].trim(),
          issuer: simpleMatch[2]?.trim() || '',
          startDate: '',
          url: ''
        });
      }
    }
  });
}

/**
 * Parse the awards section
 */
async function parseAwardsSection(content: string, result: ParsedMarkdownData): Promise<void> {
  const awardsMatch = content.match(/Awards and Recognition\s*\n-+\s*\n([\s\S]*?)(?=\n[A-Z][^:\n]*\n-+|$)/i);
  if (!awardsMatch) return;

  const awardsContent = awardsMatch[1];
  const awardLines = awardsContent.split('\n').filter(line => line.trim().startsWith('*'));
  
  awardLines.forEach(line => {
    const match = line.match(/\*\s*\*\*([^*]+)\*\*\s*–\s*(.+)/);
    if (match) {
      result.awards.push({
        title: match[1].trim(),
        date: '2015-10-26', // Default date, could be extracted if available
        awarder: 'Red Hat', // Default awarder, could be extracted if available
        summary: match[2].trim(),
        url: ''
      });
    }
  });
}
