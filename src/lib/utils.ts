/**
 * @file utils.ts
 * @description This file contains utility functions for the project.
 * @requires clsx
 * @requires tailwind-merge
 */

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * @function cn
 * @description A utility function that combines `clsx` and `tailwind-merge` to conditionally join CSS class names.
 * This is particularly useful for Tailwind CSS to handle conflicting classes gracefully.
 * @param {...ClassValue[]} inputs - An array of class values (strings, objects, arrays).
 * @returns {string} The merged and filtered CSS class names.
 * @example
 * // Basic usage
 * cn("text-red-500", "bg-blue-200"); // => "text-red-500 bg-blue-200"
 *
 * // Handling conditional classes
 * cn("p-4", { "bg-green-500": true, "text-white": false }); // => "p-4 bg-green-500"
 *
 * // Resolving conflicts (tailwind-merge)
 * cn("p-4", "p-6"); // => "p-6"
 * cn("text-red-500", "text-blue-500"); // => "text-blue-500"
 */
