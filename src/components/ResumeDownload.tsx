/**
 * @file ResumeDownload.tsx
 * @description This component provides a dropdown menu to download the resume in different formats.
 * @requires react
 * @requires @/components/ui/button
 * @requires @/components/ui/dropdown-menu
 * @requires lucide-react
 * @requires @/hooks/use-toast
 */

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Download, FileText, File, ChevronDown } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

/**
 * @interface ResumeData
 * @description Defines the structure of the resume data object.
 */
interface ResumeData {
  basics: {
    name: string;
    label: string;
    summary: string;
    location: string;
    email?: string;
    profiles: {
      linkedin: string;
      github?: string;
    };
  };
  work: Array<{
    name: string;
    position: string;
    startDate?: string;
    endDate?: string;
    current?: boolean;
    highlights: string[];
    technologies: string[];
    summary?: string;
    url?: string;
  }>;
  skills: Array<{
    name: string;
    keywords: string[];
  }>;
  publications: Array<{
    name: string;
    publisher: string;
    releaseDate: string;
    url?: string;
  }>;
  awards: Array<{
    title: string;
    date: string;
    awarder: string;
    summary: string;
    url?: string;
  }>;
  certificates: Array<{
    name: string;
    issuer: string;
    startDate?: string;
    endDate?: string;
    url?: string;
  }>;
  volunteer?: Array<{
    organization: string;
    position: string;
    startDate?: string;
    current?: boolean;
    summary: string;
    highlights: string[];
  }>;
  references?: Array<{
    name: string;
    reference: string;
  }>;
}

/**
 * @interface ResumeDownloadProps
 * @description Defines the props for the ResumeDownload component.
 */
interface ResumeDownloadProps {
  resumeData: ResumeData;
}

/**
 * @function ResumeDownload
 * @description A component that allows the user to download the resume in different formats.
 * @param {ResumeDownloadProps} props - The props for the component.
 * @returns {JSX.Element}
 */
const ResumeDownload = ({ resumeData }: ResumeDownloadProps) => {
  const [selectedFormat, setSelectedFormat] = useState<string>("json");
  const { toast } = useToast();

  /**
   * @function generateMarkdown
   * @description Generates a markdown string from the resume data.
   * @param {ResumeData} data - The resume data.
   * @returns {string}
   */
  const generateMarkdown = (data: ResumeData) => {
    let markdown = `# ${data.basics.name}\n\n`;
    markdown += `**${data.basics.label}**\n\n`;
    markdown += `${data.basics.summary}\n\n`;
    markdown += `**Location:** ${data.basics.location}\n\n`;

    if (data.basics.profiles?.linkedin) {
      markdown += `**LinkedIn:** [${data.basics.profiles.linkedin}](${data.basics.profiles.linkedin})\n`;
    }
    if (data.basics.profiles?.github) {
      markdown += `**GitHub:** [${data.basics.profiles.github}](${data.basics.profiles.github})\n`;
    }
    markdown += `\n`;

    markdown += `## Work Experience\n\n`;
    data.work.forEach((job: ResumeData['work'][number]) => {
      markdown += `### ${job.name} — ${job.position}\n`;
      const endDate = job.current ? "Present" : (job.endDate || "Present");
      markdown += `**${job.startDate || ""}${endDate ? ` – ${endDate}` : ""}**\n\n`;

      if (job.summary) {
        markdown += `${job.summary}\n\n`;
      }

      if (job.highlights && job.highlights.length > 0) {
        job.highlights.forEach((highlight: string) => {
          markdown += `* **${highlight}**\n`;
        });
        markdown += `\n`;
      }

      if (job.technologies && job.technologies.length > 0) {
        markdown += `**Skills:** ${job.technologies.join(' · ')}\n\n`;
      }
    });

    markdown += `## Technical Skills\n\n`;
    data.skills.forEach((skillGroup: ResumeData['skills'][number]) => {
      markdown += `**${skillGroup.name}:** ${skillGroup.keywords.join(', ')}\n\n`;
    });

    if (data.publications && data.publications.length > 0) {
      markdown += `## Publications\n\n`;
      markdown += `| Title | Publisher | URL |\n`;
      markdown += `|-------|-----------|-----|\n`;
      data.publications.forEach((pub: ResumeData['publications'][number]) => {
        const urlCell = pub.url ? `[Link](${pub.url})` : '';
        markdown += `| ${pub.name} | ${pub.publisher} | ${urlCell} |\n`;
      });
      markdown += `\n`;
    }

    if (data.certificates && data.certificates.length > 0) {
      markdown += `## Certifications\n\n`;
      data.certificates.forEach((cert: ResumeData['certificates'][number]) => {
        const certLine = cert.url ?
          `* [${cert.name}](${cert.url}) – ${cert.issuer}` :
          `* ${cert.name} – ${cert.issuer}`;
        if (cert.startDate) {
          markdown += `${certLine}, issued ${cert.startDate}\n`;
        } else {
          markdown += `${certLine}\n`;
        }
      });
      markdown += `\n`;
    }

    if (data.awards && data.awards.length > 0) {
      markdown += `## Awards and Recognition\n\n`;
      data.awards.forEach((award: ResumeData['awards'][number]) => {
        markdown += `* **${award.title}** – ${award.summary}\n`;
      });
      markdown += `\n`;
    }

    return markdown;
  };

  /**
   * @function downloadFile
   * @description Downloads a file with the given content, filename, and type.
   * @param {string} content - The content of the file.
   * @param {string} filename - The name of the file.
   * @param {string} type - The MIME type of the file.
   */
  const downloadFile = (content: string, filename: string, type: string) => {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  /**
   * @function generateFilename
   * @description Generates a filename for the resume file.
   * @param {string} extension - The file extension.
   * @returns {string}
   */
  const generateFilename = (extension: string) => {
    const name = resumeData.basics.name.replace(/\s+/g, '_');
    return `${name}_Resume.${extension}`;
  };

  /**
   * @function handleDownload
   * @description Handles the download of the resume file based on the selected format.
   */
  const handleDownload = () => {
    switch (selectedFormat) {
      case "json":
        downloadFile(
          JSON.stringify(resumeData, null, 2),
          generateFilename("json"),
          "application/json",
        );
        toast({
          title: "Resume Downloaded",
          description: "JSON format downloaded successfully",
        });
        break;

      case "markdown": {
        const markdownContent = generateMarkdown(resumeData);
        downloadFile(
          markdownContent,
          generateFilename("md"),
          "text/markdown",
        );
        toast({
          title: "Resume Downloaded",
          description: "Markdown format downloaded successfully",
        });
        break;
      }





      default:
        break;
    }
  };

  /**
   * @function handleFormatSelect
   * @description Handles the selection of a download format and initiates the download.
   * @param {string} format - The selected format.
   */
  const handleFormatSelect = (format: string) => {
    setSelectedFormat(format);
    handleDownload();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Download Resume
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">

        <DropdownMenuItem onClick={() => handleFormatSelect("json")}>
          <File className="h-4 w-4 mr-2" />
          JSON
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleFormatSelect("markdown")}>
          <FileText className="h-4 w-4 mr-2" />
          Markdown (Generated)
        </DropdownMenuItem>

      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ResumeDownload;
