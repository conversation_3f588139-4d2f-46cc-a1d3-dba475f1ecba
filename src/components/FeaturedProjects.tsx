/**
 * @file FeaturedProjects.tsx
 * @description This component displays a list of featured projects with a dialog to view more details.
 * @requires react
 * @requires @/components/ui/card
 * @requires @/components/ui/badge
 * @requires @/components/ui/button
 * @requires @/components/ui/dialog
 * @requires lucide-react
 */

import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ExternalLink, Github, Folder, Calendar, Users, Clock, Building } from "lucide-react";

/**
 * @function FeaturedProjects
 * @description A component that displays a list of featured projects.
 * @returns {JSX.Element}
 */
const FeaturedProjects = () => {
  const [selectedProject, setSelectedProject] = useState(null);

  /**
   * @description An array of project objects.
   * @type {Array<Object>}
   */
  const projects = [
    {
      id: 1,
      title: "Enterprise Cloud-Native Platform",
      description:
        "A comprehensive cloud-native platform built on OpenShift and AWS, enabling enterprises to modernize applications with containerization and microservices architecture. This platform enables Fortune 500 companies to accelerate their digital transformation journey.",
      detailedDescription: `This enterprise-grade cloud-native platform represents a complete digital transformation solution for large-scale organizations. Built on Red Hat OpenShift and AWS, it provides a robust foundation for containerized applications with advanced DevSecOps capabilities.

Key achievements include:
• 40% reduction in deployment time through automated CI/CD pipelines
• 99.9% uptime with zero-downtime deployments
• 60% cost optimization through intelligent resource management
• Enhanced security posture with integrated vulnerability scanning
• Scalable architecture supporting 1000+ microservices

The platform integrates cutting-edge technologies including Istio service mesh for traffic management, ArgoCD for GitOps-based deployments, and comprehensive observability through Prometheus and Grafana. Terraform infrastructure-as-code ensures consistent and repeatable deployments across multiple environments.`,
      image:
        "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=800&h=600&fit=crop",
      technologies: [
        "OpenShift",
        "AWS",
        "Kubernetes",
        "ArgoCD",
        "Istio",
        "Terraform",
        "Tekton",
        "Prometheus",
        "Grafana",
        "OpenTelemetry",
      ],
      category: "Cloud Architecture",
      year: "2024",
      duration: "8 months",
      role: "Principal Architect",
      teamSize: "10 engineers",
      client: "Fortune 500 Financial Services",
      liveUrl: "https://www.redhat.com/en/technologies/cloud-computing/openshift",
      githubUrl: "https://github.com/openshift",
      challenges: [
        "Legacy system integration with modern cloud-native architecture",
        "Ensuring zero-downtime migration of critical financial services",
        "Implementing comprehensive security and compliance frameworks",
        "Scaling to handle peak loads of 100,000+ concurrent users"
      ],
      outcomes: [
        "40% faster time-to-market for new features",
        "60% reduction in infrastructure costs",
        "99.9% system availability achieved",
        "Enhanced developer productivity with self-service capabilities"
      ]
    },
    {
      id: 2,
      title: "AI/ML Model Serving Platform",
      description:
        "A production-grade platform for deploying, managing, and monitoring machine learning models at scale with MLOps best practices. Enables healthcare providers to deploy AI models for patient care optimization.",
      detailedDescription: `A comprehensive MLOps platform designed specifically for healthcare applications, enabling rapid deployment and management of AI/ML models in production environments. Built on Kubeflow and AWS services, this platform ensures HIPAA compliance while delivering high-performance model serving.

Platform capabilities include:
• Automated model deployment with A/B testing and canary releases
• Real-time model monitoring and drift detection
• Scalable inference serving handling 10,000+ requests per second
• Comprehensive audit trails for regulatory compliance
• Integration with existing healthcare IT systems

The platform leverages AWS Bedrock for foundation models, SageMaker for training pipelines, and KServe for high-performance model serving. MLflow provides experiment tracking and model versioning, while custom monitoring solutions ensure model performance and data quality.`,
      image:
        "https://images.unsplash.com/photo-1518770660439-4636190af475?w=800&h=600&fit=crop",
      technologies: [
        "Kubeflow",
        "AWS Bedrock",
        "AWS SageMaker",
        "MLflow",
        "KServe",
        "Prometheus",
        "Grafana",
        "TensorFlow",
        "PyTorch",
        "ONNX",
        "Python",
        "Docker",
      ],
      category: "AI/ML",
      year: "2023",
      duration: "6 months",
      role: "Lead Platform Architect",
      teamSize: "6 engineers",
      client: "Leading Healthcare Provider",
      liveUrl: "https://www.kubeflow.org/",
      githubUrl: "https://github.com/kubeflow/kubeflow",
      challenges: [
        "HIPAA compliance requirements for healthcare data",
        "Real-time inference with sub-100ms latency requirements",
        "Integration with legacy healthcare information systems",
        "Ensuring model explainability for clinical decision support"
      ],
      outcomes: [
        "50% improvement in diagnostic accuracy for radiology workflows",
        "30% reduction in patient wait times through predictive scheduling",
        "HIPAA-compliant AI platform serving 500+ healthcare facilities",
        "99.95% model serving uptime with automated failover"
      ]
    },
    {
      id: 3,
      title: "Hybrid Cloud Data Fabric",
      description:
        "A unified data access layer enabling seamless data movement and analytics across on-premises and multi-cloud environments. Provides real-time insights for global retail operations.",
      detailedDescription: `An enterprise-scale data fabric solution that unifies data access and analytics across hybrid and multi-cloud environments. This platform enables real-time data processing and analytics for a global retail enterprise with operations spanning 50+ countries.

Architecture highlights include:
• Real-time data streaming with Apache Kafka and Debezium CDC
• Unified analytics layer with Apache Spark and Presto
• Data governance and lineage tracking with Apache Atlas
• Self-service analytics with ThoughtSpot integration
• Cross-cloud data replication and synchronization

The platform processes over 10TB of data daily, providing real-time insights for inventory management, customer analytics, and supply chain optimization. Delta Lake ensures ACID transactions while maintaining high performance for both batch and streaming workloads.`,
      image:
        "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=600&fit=crop",
      technologies: [
        "Red Hat OpenShift Data Science",
        "Apache Kafka",
        "Debezium",
        "Apache Spark",
        "Presto",
        "Apache Atlas",
        "ThoughtSpot",
        "AWS S3",
        "Azure Data Lake",
        "Apache NiFi",
        "Delta Lake",
      ],
      category: "Data Architecture",
      year: "2023",
      duration: "9 months",
      role: "Principal Data Architect",
      teamSize: "8 engineers",
      client: "Global Retail Enterprise",
      liveUrl: "https://spark.apache.org/",
      githubUrl: "https://github.com/apache/spark",
      challenges: [
        "Real-time data processing across multiple cloud providers",
        "Ensuring data consistency in distributed environments",
        "Implementing data governance across hybrid infrastructure",
        "Optimizing query performance for petabyte-scale datasets"
      ],
      outcomes: [
        "75% faster analytics query performance",
        "Unified data access across 15+ data sources",
        "Real-time insights enabling $50M+ in cost savings",
        "Automated data quality monitoring with 99.9% accuracy"
      ]
    },
    {
      id: 4,
      title: "Zero-Trust Security Framework",
      description:
        "A comprehensive zero-trust security implementation for cloud-native applications with continuous verification and least-privilege access. Provides enterprise-grade security for sensitive workloads.",
      detailedDescription: `A comprehensive zero-trust security architecture designed for government and enterprise environments requiring the highest levels of security. This framework implements continuous verification, least-privilege access, and defense-in-depth strategies across cloud-native applications.

Security features include:
• Identity-based access control with continuous verification
• Mutual TLS (mTLS) for all service-to-service communication
• Policy-as-code with Open Policy Agent for fine-grained authorization
• Runtime security monitoring with Falco and behavioral analysis
• Automated vulnerability scanning and remediation

The framework leverages SPIFFE/SPIRE for workload identity, Istio service mesh for traffic encryption, and HashiCorp Vault for secrets management. Integration with Keycloak provides enterprise identity federation while maintaining zero-trust principles.`,
      image:
        "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=600&fit=crop",
      technologies: [
        "Istio",
        "SPIFFE/SPIRE",
        "Open Policy Agent",
        "Vault",
        "Keycloak",
        "mTLS",
        "OAuth2",
        "OpenID Connect",
        "Falco",
        "Trivy",
      ],
      category: "Security",
      year: "2024",
      duration: "5 months",
      role: "Security Architect",
      teamSize: "4 engineers",
      client: "Government Agency",
      liveUrl: "https://istio.io/",
      githubUrl: "https://github.com/istio/istio",
      challenges: [
        "Implementing zero-trust without impacting application performance",
        "Meeting strict government security compliance requirements",
        "Integrating with legacy authentication systems",
        "Ensuring seamless user experience with enhanced security"
      ],
      outcomes: [
        "100% reduction in lateral movement attack vectors",
        "FedRAMP High authorization achieved",
        "Zero security incidents in 18 months of operation",
        "50% reduction in security audit preparation time"
      ]
    },
    {
      id: 5,
      title: "Cloud FinOps Optimization Platform",
      description:
        "An intelligent cost optimization platform leveraging AI to analyze cloud spending patterns and provide automated recommendations for cost reduction across multi-cloud environments.",
      detailedDescription: `An AI-powered FinOps platform that provides intelligent cost optimization across AWS, Azure, and Google Cloud Platform. This solution combines machine learning algorithms with real-time cost monitoring to deliver automated recommendations and cost-saving actions.

Platform capabilities include:
• Real-time cost monitoring and anomaly detection
• AI-driven rightsizing recommendations for compute resources
• Automated scheduling for non-production environments
• Reserved instance and savings plan optimization
• Multi-cloud cost allocation and chargeback reporting

The platform integrates with native cloud billing APIs and uses TensorFlow for predictive cost modeling. React-based dashboards with D3.js visualizations provide intuitive cost insights, while automated actions can achieve 20-30% cost reductions without manual intervention.`,
      image:
        "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop",
      technologies: [
        "AWS Cost Explorer API",
        "AWS Lambda",
        "EventBridge",
        "Kinesis",
        "Azure Cost Management",
        "GCP Billing API",
        "Python",
        "TensorFlow",
        "React",
        "D3.js",
        "PostgreSQL",
      ],
      category: "Cloud FinOps",
      year: "2024",
      duration: "4 months",
      role: "Cloud Architect",
      teamSize: "3 engineers",
      client: "Enterprise SaaS Company",
      liveUrl: "https://aws.amazon.com/aws-cost-management/",
      githubUrl: "https://github.com/aws/aws-cost-explorer-api",
      challenges: [
        "Correlating cost data across multiple cloud providers",
        "Implementing automated cost optimization without service disruption",
        "Providing accurate cost forecasting with seasonal variations",
        "Balancing cost optimization with performance requirements"
      ],
      outcomes: [
        "35% reduction in overall cloud spending",
        "Automated cost optimization saving 200+ hours monthly",
        "Real-time cost visibility across 500+ AWS accounts",
        "ROI of 800% within first 6 months of implementation"
      ]
    },
  ];

  return (
    <section className="py-16 px-6">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 mb-4 text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
            <Folder className="h-4 w-4" />
            Featured Work
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Projects That Define My Expertise
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            A showcase of my best work, demonstrating technical skills and
            creative problem-solving across cloud platforms, AI/ML, and
            enterprise architecture.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {projects.map((project) => (
            <Card
              key={project.id}
              className="overflow-hidden hover:shadow-lg transition-shadow duration-300 group"
            >
              <div className="relative overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <Badge className="bg-blue-600 text-white">
                    {project.category}
                  </Badge>
                </div>

              </div>

              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {project.title}
                  </h3>
                  <span className="text-sm text-gray-500">{project.year}</span>
                </div>

                <p className="text-gray-600 mb-4 leading-relaxed">
                  {project.description}
                </p>

                {/* Project metadata */}
                <div className="grid grid-cols-2 gap-2 mb-4 text-xs text-gray-500">
                  <div>Role: {project.role}</div>
                  <div>Duration: {project.duration}</div>
                  <div>Team: {project.teamSize}</div>
                  <div>Client: {project.client}</div>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.slice(0, 6).map((tech, techIndex) => (
                    <Badge
                      key={techIndex}
                      variant="outline"
                      className="text-xs"
                    >
                      {tech}
                    </Badge>
                  ))}
                  {project.technologies.length > 6 && (
                    <Badge variant="outline" className="text-xs">
                      +{project.technologies.length - 6} more
                    </Badge>
                  )}
                </div>

                <div className="pt-4">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-600 hover:text-blue-700"
                      >
                        View Details
                        <ExternalLink className="h-4 w-4 ml-2" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="text-2xl font-bold text-gray-900">
                          {project.title}
                        </DialogTitle>
                        <DialogDescription className="text-lg text-gray-600">
                          {project.description}
                        </DialogDescription>
                      </DialogHeader>

                      <div className="space-y-6">
                        {/* Project Image */}
                        <div className="relative overflow-hidden rounded-lg">
                          <img
                            src={project.image}
                            alt={project.title}
                            className="w-full h-64 object-cover"
                          />
                          <div className="absolute top-4 left-4">
                            <Badge className="bg-blue-600 text-white">
                              {project.category}
                            </Badge>
                          </div>
                        </div>

                        {/* Project Metadata */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Calendar className="h-4 w-4" />
                            <span>{project.year} • {project.duration}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Users className="h-4 w-4" />
                            <span>{project.teamSize}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Building className="h-4 w-4" />
                            <span>{project.role}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Clock className="h-4 w-4" />
                            <span>{project.client}</span>
                          </div>
                        </div>

                        {/* Detailed Description */}
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">
                            Project Overview
                          </h3>
                          <div className="text-gray-600 leading-relaxed whitespace-pre-line">
                            {project.detailedDescription}
                          </div>
                        </div>

                        {/* Technologies */}
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">
                            Technologies Used
                          </h3>
                          <div className="flex flex-wrap gap-2">
                            {project.technologies.map((tech, techIndex) => (
                              <Badge
                                key={techIndex}
                                variant="outline"
                                className="text-sm"
                              >
                                {tech}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Challenges */}
                        {project.challenges && (
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-3">
                              Key Challenges
                            </h3>
                            <ul className="space-y-2">
                              {project.challenges.map((challenge, index) => (
                                <li key={index} className="flex items-start gap-2 text-gray-600">
                                  <span className="text-blue-600 mt-1">•</span>
                                  <span>{challenge}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {/* Outcomes */}
                        {project.outcomes && (
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-3">
                              Key Outcomes
                            </h3>
                            <ul className="space-y-2">
                              {project.outcomes.map((outcome, index) => (
                                <li key={index} className="flex items-start gap-2 text-gray-600">
                                  <span className="text-green-600 mt-1">✓</span>
                                  <span>{outcome}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}


                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </Card>
          ))}
        </div>


      </div>
    </section>
  );
};

export default FeaturedProjects;
