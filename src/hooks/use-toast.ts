/**
 * @file use-toast.ts
 * @description This file provides a custom hook and utility functions for managing toast notifications.
 * It implements a state management pattern for toasts, allowing them to be added, updated, and dismissed.
 * @requires react
 * @requires @/components/ui/toast
 */

import * as React from "react";

import type { ToastActionElement, ToastProps } from "@/components/ui/toast";

const TOAST_LIMIT = 1;
const TOAST_REMOVE_DELAY = 1000000;

/**
 * @typedef {Object} ToasterToast
 * @property {string} id - Unique ID for the toast.
 * @property {React.ReactNode} [title] - Title of the toast.
 * @property {React.ReactNode} [description] - Description of the toast.
 * @property {ToastActionElement} [action] - Action element for the toast.
 * @description Type definition for a toast object, extending ToastProps.
 */
type ToasterToast = ToastProps & {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToastActionElement;
};

// const actionTypes = {
//   ADD_TOAST: "ADD_TOAST",
//   UPDATE_TOAST: "UPDATE_TOAST",
//   DISMISS_TOAST: "DISMISS_TOAST",
//   REMOVE_TOAST: "REMOVE_TOAST",
// } as const; // Removed as it's unused as a value

let count = 0;

/**
 * @function genId
 * @description Generates a unique ID for a toast.
 * @returns {string}
 */
function genId() {
  count = (count + 1) % Number.MAX_SAFE_INTEGER;
  return count.toString();
}

// type ActionType = typeof actionTypes; // Removed

type Action =
  | {
      type: "ADD_TOAST";
      toast: ToasterToast;
    }
  | {
      type: "UPDATE_TOAST";
      toast: Partial<ToasterToast>;
    }
  | {
      type: "DISMISS_TOAST";
      toastId?: ToasterToast["id"];
    }
  | {
      type: "REMOVE_TOAST";
      toastId?: ToasterToast["id"];
    };

/**
 * @interface State
 * @description Defines the shape of the toast state.
 * @property {ToasterToast[]} toasts - An array of active toasts.
 */
interface State {
  toasts: ToasterToast[];
}

/**
 * @description A map to store timeouts for toasts, used for automatic removal.
 */
const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();

/**
 * @function addToRemoveQueue
 * @description Adds a toast to the removal queue after a specified delay.
 * @param {string} toastId - The ID of the toast to remove.
 */
const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return;
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId);
    dispatch({
      type: "REMOVE_TOAST",
      toastId: toastId,
    });
  }, TOAST_REMOVE_DELAY);

  toastTimeouts.set(toastId, timeout);
};

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST":
      return {
        ...state,
        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),
      };

    case "UPDATE_TOAST":
      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === action.toast.id ? { ...t, ...action.toast } : t,
        ),
      };

    case "DISMISS_TOAST": {
      const { toastId } = action;

      // ! Side effects ! - This could be extracted into a dismissToast() action,
      // but I'll keep it here for simplicity
      if (toastId) {
        addToRemoveQueue(toastId);
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id);
        });
      }

      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === toastId || toastId === undefined
            ? {
                ...t,
                open: false,
              }
            : t,
        ),
      };
    }
    case "REMOVE_TOAST":
      if (action.toastId === undefined) {
        return {
          ...state,
          toasts: [],
        };
      }
      return {
        ...state,
        toasts: state.toasts.filter((t) => t.id !== action.toastId),
      };
  }
};

/**
 * @description An array of listeners that are called when the state changes.
 */
const listeners: Array<(state: State) => void> = [];

/**
 * @description The current state of the toasts.
 */
let memoryState: State = { toasts: [] };

/**
 * @function dispatch
 * @description Dispatches an action to update the toast state.
 * @param {Action} action - The action to dispatch.
 */
function dispatch(action: Action) {
  memoryState = reducer(memoryState, action);
  listeners.forEach((listener) => {
    listener(memoryState);
  });
}

type Toast = Omit<ToasterToast, "id">;

/**
 * @function toast
 * @description Creates and displays a toast notification.
 * @param {Toast} props - The properties of the toast.
 * @returns {{ id: string; dismiss: () => void; update: (props: ToasterToast) => void; }}
 */
function toast({ ...props }: Toast) {
  const id = genId();

  const update = (props: ToasterToast) =>
    dispatch({
      type: "UPDATE_TOAST",
      toast: { ...props, id },
    });
  const dismiss = () => dispatch({ type: "DISMISS_TOAST", toastId: id });

  dispatch({
    type: "ADD_TOAST",
    toast: {
      ...props,
      id,
      open: true,
      onOpenChange: (open) => {
        if (!open) dismiss();
      },
    },
  });

  return {
    id: id,
    dismiss,
    update,
  };
}

/**
 * @function useToast
 * @description A custom hook that provides access to the toast state and functions.
 * @returns {{ toasts: ToasterToast[]; toast: typeof toast; dismiss: (toastId?: string) => void; }}
 */
function useToast() {
  const [state, setState] = React.useState<State>(memoryState);

  React.useEffect(() => {
    listeners.push(setState);
    return () => {
      const index = listeners.indexOf(setState);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  }, [state]);

  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
  };
}

export { useToast, toast };
