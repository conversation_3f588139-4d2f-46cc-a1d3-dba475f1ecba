/**
 * @file use-mobile.tsx
 * @description This file contains a custom hook to detect if the user is on a mobile device.
 * @requires react
 */

import * as React from "react";

const MOBILE_BREAKPOINT = 768;

/**
 * @function useIsMobile
 * @description A custom hook that returns true if the user is on a mobile device.
 * @returns {boolean}
 */
export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(
    undefined,
  );

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };
    mql.addEventListener("change", onChange);
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    return () => mql.removeEventListener("change", onChange);
  }, []);

  return !!isMobile;
}
