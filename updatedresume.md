<PERSON>
====================

Principal – Platform Engineering and Modernization | Cloud & Platform Engineering

[github.com/nicholas-gerasimatos](https://www.google.com/search?q=https://github.com/nicholas-gerasimatos) 

[linkedin.com/in/nicholas-gerasimatos](https://www.google.com/search?q=https://linkedin.com/in/nicholas-gerasimatos)

Professional Summary
------------------

Possessing over two decades of distinguished experience, I am an accomplished platform-focused technology leader specializing in the construction and modernization of large-scale cloud and data platforms for Fortune 500 corporations and public-sector entities. I seamlessly integrate profound open-source expertise with decisive leadership to deliver secure, highly resilient, scalable, and cost-optimized solutions across diverse enterprise environments, including AWS, Azure, GCP, Red Hat OpenShift, and various hybrid infrastructures. My core proficiency lies in translating intricate technical concepts into quantifiable business value, cultivating high-performing, cross-functional teams through structured mentorship, and consistently advancing continuous improvement via Agile, DevSecOps, and GitOps methodologies. I have a proven record in designing, implementing, and expertly directing modernization efforts that minimize operational disruption, consistently yielding measurable business results and cultivating long-term organizational advantage. As a recognized thought leader and frequent industry speaker, I actively contribute to cloud and AI discourse through influential publications and engaging presentations at prominent global conferences.

Work Experience
--------------

AHEAD — Principal – Platform Engineering and Modernization

May 2024 – Present
-----------------

* **Emerging Technologies Expertise:** Maintain a leading-edge understanding of emerging technologies, with a particular focus on artificial intelligence, and machine learning. Implement AI-driven solutions that streamline operations, and unlock new business value.
* **Advanced Solution Development:** Architect, design, and implement cutting-edge solutions leveraging open-source frameworks and cloud-native technologies, including Infrastructure as Code (IaC), Platform as a Service (PaaS), and Infrastructure as a Service (IaaS). Integrate advanced AI/ML and AI capabilities to enhance system intelligence, automate workflows, and drive improvements in performance, scalability, and cost efficiency.
* **Cloud and Hybrid Architecture Optimization:** Optimize enterprise cloud and hybrid infrastructures by applying distributed computing principles and best practices. Conduct in-depth architectural assessments to identify bottlenecks, reduce latency, and maximize system uptime. Deliver robust, resilient solutions that support critical business operations and ensure seamless scalability for large-scale enterprise clients.
* **Effective Technical Communication:** Translate complex technical concepts into clear, actionable insights for both technical and non-technical stakeholders. Develop and deliver compelling presentations and technical documentation to secure executive buy-in, expedite project approvals, and accelerate delivery timelines.
* **Leadership in Cross-Functional Delivery:** Lead diverse, cross-functional teams through the full project lifecycle, from initial scoping to final delivery. Utilize agile methodologies and GitOps practices to drive collaboration, transparency, and continuous improvement.
* **Advocacy for Best Practices:** Actively champion industry best practices in cloud architecture and software delivery. Mentor teams, develop and disseminate technical guidelines, and establish governance frameworks to elevate the quality and consistency of project outcomes.

**Skills:** Amazon Web Services (AWS) · Systems Engineering · System Architecture · Kubernetes · Software Development · Software Defined Storage · Amazon EKS · Large Scale Systems · IaaS · Hybrid Cloud · Distributed Systems · Continuous Integration · Containers · Consulting · Cloud Computing · Artificial Intelligence (AI) · Machine Learning · DevOps · Open-Source Software · Linux · GitOps · Large Language Models (LLM)

Amazon Web Services — Partner Cloud Architect

2022 – 2024
-----------

* **Cloud Solution Architecture and Optimization:** Design, implement, and optimize scalable, secure, and cost-effective AWS cloud solutions with a focus on resiliency, high availability, performance, and cost efficiency. Leverage ROSA/OpenShift, Bedrock, SageMaker, EMR, Athena, and various other AWS services to deliver intelligent, automated solutions that drive business value.
* **Cross-Functional Collaboration:** Collaborate with partners, customers, and cross-functional teams to understand diverse requirements and translate them into robust cloud architectures.
* **Complex Cloud Infrastructure Deployment:** Architect and deploy sophisticated cloud infrastructures tailored to client needs, ensuring seamless integration of AI/ML and generative AI solutions for improved performance and automation.
* **Training and Enablement Leadership:** Lead training sessions and workshops for internal teams and clients, focusing on Red Hat, Open-Source, and AWS architecture best practices, security compliance, and the adoption of emerging technologies.
* **Partner and Community Engagement:** Conduct internal and partner training, participate in industry events, and engage in global webinars and roadshows to showcase innovative AWS and partner solutions.
* **Technical Curriculum Development:** Build and implement comprehensive education programs for technical teams, including Immersion Days, Game Days, workshops, and technical documentation that highlight AWS services and partner integrations.
* **Sales Enablement and Competitive Positioning:** Create concise battle cards to showcase partner solutions differentiators and value. Develop playbooks outlining synergies between partner offerings and AWS services.
* **Customer Advocacy and Success Story Development:** Collect and curate customer references and success stories, including co-branded content and showcases of GSI/ISV-developed solutions that demonstrate the impact of AWS and partner driven cloud transformations.

**Skills:** Amazon Web Services (AWS) · Red Hat OpenShift · AWS Bedrock · Cloud Architecture · Scalability · Security · Cost Optimization · Resiliency · High Availability · Performance Tuning · Partner Enablement · Technical Presentations · Customer Success · AWS Well-Architected Framework · Kubernetes · Containers · Automation · AI/ML Integration

Red Hat — Emerging Technologies Cloud Engineer

2015 – 2022
-----------

* **Strategic Cloud Modernization and Digital Transformation:** Lead modernization and digital transformation initiatives by driving strategic cloud adoption. Unlock new business opportunities and deliver measurable business benefits through the integration of emerging technologies, blockchain, edge computing, OpenShift, AI, ML, and serverless architectures. Enable organizations to become more agile and future-ready.
* **Stakeholder Engagement and Consensus Building:** Clearly articulate the value of cloud solutions and emerging technologies to a wide range of stakeholders. Address concerns, answer technical and business questions, and build consensus to ensure buy-in from executive, technical, and operational teams.
* **Seamless Transition to Cloud-Centric Operating Models:** Facilitate smooth transitions to cloud-centric operating models by ensuring effective integration of advanced technologies such as containers, serverless, and AI/ML into existing and new workflows. Oversee the adoption of hybrid and multi-cloud strategies to maximize flexibility and support ongoing innovation.
* **Cloud Readiness Assessments and Migration Strategy:** Develop and implement tailored migration strategies and multiphase roadmaps that align short-term wins with long-term business objectives. Ensure each migration is smooth and cost-effective.
* **Thought Leadership and Industry Influence:** Establish thought leadership by publishing technical articles, delivering presentations at industry conferences, and actively participating in cloud and emerging technology communities. Share insights on digital transformation, cloud adoption, and practical applications of emerging technologies.
* **Collaboration and Innovation Culture:** Build strong partnerships with internal teams, cross-functional stakeholders, and technology vendors to drive continuous innovation. Foster a culture of learning and collaboration that encourages exploration and adoption of emerging technologies in cloud-based solutions.

**Skills:** Cloud Adoption · Digital Transformation · Edge Computing · Blockchain · AI/ML · Serverless · OpenShift · Technical Writing · Public Speaking · AWS re:Invent · KubeCon + CloudNativeCon · Red Hat Summit · IBM Think · Agile Methodologies · DevOps · Continuous Learning · Cloud-Native Architectures · Microservices · Open Source Advocacy

FICO — Director of Cloud Service Platforms

2012 – 2015
-----------

* **Strategic Cloud Platform Planning and Implementation:** Lead the strategic planning, design, and implementation of cloud service platforms for organizations of varying scale and complexity. Align cloud solutions with business needs to ensure robust, scalable, and secure platforms that support growth and innovation.
* **Digital Transformation and Service Delivery:** Drive digital transformation initiatives by optimizing infrastructure performance and ensuring seamless delivery of cloud services to both internal and external stakeholders. Focus on enhancing agility, reliability, and user experience across the organization.
* **Comprehensive Cloud Strategy Development:** Develop and execute comprehensive cloud strategies that align with organizational objectives. Lead cross-functional teams to deliver cloud services that meet business goals, enhance operational efficiency, and foster a culture of innovation.
* **Expertise in Cloud Service Models:** Demonstrate deep expertise in infrastructure-as-a-service (IaaS), platform-as-a-service (PaaS), and software-as-a-service (SaaS) models. Select and implement effective service models to meet diverse business requirements.
* **Team Leadership and Talent Development:** Foster a high-performance culture by building collaborative teams, recruiting top talent, and providing mentorship and professional development opportunities. Empower team members to excel in cloud technologies and deliver outstanding results.
* **Security, Compliance, and Data Protection:** Ensure compliance with industry standards and regulations by establishing robust security controls and data protection measures. Proactively manage risk and safeguard organizational data across all cloud environments.
* **Vendor and Partner Relationship Management:** Establish and maintain strong relationships with cloud service providers, vendors, and partners. Manage service level agreements (SLAs), and drive value-added partnerships that support organizational goals and deliver long-term value.

**Skills:** Cloud Platforms · Strategic Planning · Digital Transformation · Infrastructure Optimization · Vendor Management · Budget Management · Regulatory Compliance · Data Protection · Security Standards · Team Leadership · Mentorship · Platform Growth

American Express — Senior Data Architect

2009 – 2012
-----------

* **Data Architecture Leadership & Implementation:** Guided data architecture initiatives, encompassing the comprehensive design and precise implementation of scalable, performant, and secure data solutions that demonstrably yielded critical data-driven insights and significant business value. Collaborated with cross-functional teams to translate diverse requirements into robust, actionable data solutions aligned with strategic organizational goals.
* **Advanced Analytics & Enablement:** Developed and deployed scalable, secure data solutions specifically engineered to power advanced analytics, machine learning initiatives, and real-time decision-making processes across the organization, driving actionable intelligence. Designed extensive big-data environments, strategically leveraging technologies such as Hadoop and Spark, to effectively facilitate the acquisition, transformation, and analysis of high-volume, high-velocity, and high-variety datasets.
* **Complex Problem Resolution & Optimization:** Applied established data architecture principles and a wide range of cutting-edge technologies to effectively resolve complex business challenges related to data management and utilization. Identified root causes of data-related bottlenecks and inefficiencies, proposing innovative solutions balancing immediate needs with long-term sustainability.
* **Data Modeling & Governance:** Formulated conceptual, logical, and physical data models with precision, ensuring robust data integrity, optimal accessibility, consistent data quality, and optimal organization for diverse analytical workloads. Aligned data architectures with enterprise-wide data governance standards and incorporated long-term scalability considerations.
* **Regulatory Expertise & Cloud Optimization:** Demonstrated profound expertise in navigating complex data privacy regulations, implementing stringent security frameworks, and applying effective access control methodologies to safeguard sensitive information. Possessed extensive experience optimizing cost and performance through the strategic selection of cloud services and technologies.

**Skills:** Data Architecture · Scalable Solutions · Data Security · Data Analytics · Real-time Decision Making · Hadoop · Spark · Big Data · Machine Learning · Data Privacy · Regulatory Compliance · Data Governance · Data Modeling · System Design

VCE — Principal Architect

2008 – 2011
-----------

* **Solution Design & Implementation Leadership:** Led the design and implementation of complex and innovative solutions, collaborating with cross-functional teams and stakeholders to align technology initiatives with business goals, enhance performance, and drive organizational growth.
* **Architectural Expertise:** Demonstrated expertise in architecting scalable, reliable, and secure solutions across a wide range of domains and technologies, with a deep understanding of architectural patterns, best practices, and industry standards.
* **Strategic Roadmapping & Team Development:** Developed technology roadmaps and long-term architectural visions, leading and mentoring teams to foster a culture of innovation and drive initiatives that deliver value to the organization.
* **Comprehensive Technology Knowledge:** Possessed comprehensive knowledge of various technologies, frameworks, and platforms across cloud computing, distributed systems, microservices, and enterprise architecture, leveraging emerging technologies to drive business innovation and competitive advantage.
* **System Optimization & Integration:** Designed and implemented large-scale, distributed, and highly available systems, integrating disparate systems and ensuring interoperability, with proficiency in scalability, performance optimization, and capacity planning.
* **Stakeholder Engagement & Communication:** Demonstrated excellent communication and interpersonal skills, engaging and influencing stakeholders at all levels of the organization, building relationships, fostering collaboration, and presenting complex technical concepts in a clear and concise manner.

**Skills:** Converged Infrastructure · Reference Architectures · Solution Architecture · Scalability · Reliability · Security · Technology Roadmapping · Distributed Systems · High Availability · System Integration · Interoperability · Performance Optimization · Mentorship · Cross-functional Collaboration

Microsoft — Senior System Engineer

2005 – 2007
-----------

* **Platform Design & Management:** Designed, implemented, and managed the Microsoft CORE software development infrastructure platform, focusing on optimizing its performance, security, and reliability for global teams.
* **Technology Integration & Optimization:** Leveraged a strategic combination of Microsoft and open-source technologies to enhance system performance, bolster security posture, and streamline operational workflows.
* **Project Leadership & Collaboration:** Drove successful projects and collaborated effectively with cross-functional teams to deliver innovative solutions that were meticulously aligned with overarching business objectives.
* **Microsoft Technology Expertise:** Demonstrated deep knowledge and hands-on experience with core Microsoft technologies, ensuring proficient design, configuration, and management of Microsoft-based systems and services.
* **System Architecture & Scalability:** Architected and designed scalable and resilient systems predicated upon Microsoft technologies, with considerable expertise in capacity planning, performance tuning, and optimizing infrastructure for maximum efficiency and availability.
* **Hybrid Cloud & Security Implementation:** Integrated on-premises systems with Microsoft Internal Cloud, demonstrating proficiency in hybrid cloud design, migration strategies, and the comprehensive management of cloud-based infrastructure. Possessed a strong understanding of Microsoft security technologies and best practices, including Identity and Access Management, Active Directory security, and data protection, and consistently implemented robust security controls to ensure compliance with industry standards.
* **Problem Solving & Analysis:** Demonstrated exceptional analytical and problem-solving skills, enabling the effective diagnosis and resolution of complex technical challenges.

**Skills:** Systems Engineering · Infrastructure Management · Performance Optimization · Security Enhancement · Microsoft Technologies · Open Source Integration · Scalable Systems · Resilient Systems · Automation · Compliance · Capacity Planning · Performance Tuning · Infrastructure Optimization · Windows Server · Active Directory · Microsoft Exchange · Microsoft 365 · Azure · PowerShell · Hybrid Cloud · Migration Strategies · Identity and Access Management · Data Protection · Problem Solving · Analytical Skills

Key Projects
------------

Project

Role & Impact

Enterprise Cloud-Native Platform (OpenShift + AWS)

Containerized 120 applications; achieved a 70% reduction in deployment lead-time.

AI/ML Model-Serving Platform

Delivered a HIPAA-compliant MLOps stack, resulting in a 40% decrease in inference latency.

Hybrid-Cloud Data Fabric

Unified on-premise and cloud data, enabling real-time inventory insights.

Zero-Trust Security Framework

Implemented continuous verification, resulting in zero critical Common Vulnerabilities and Exposures (CVEs) post-launch.

Cloud FinOps Optimization Platform

Developed an AI-driven recommender that reduced multi-cloud expenditures by 22% within the first quarter.

Certifications
--------------

AI / ML
-------

* [Build Rich-Context AI Apps (MCP)](https://www.anthropic.com/certification/mcp) – Anthropic, issued January 2024
* [Agent Communication Protocol (ACP)](https://www.deeplearning.ai/short-courses/agent-communication-protocol/) – DeepLearning.AI, issued January 2024

Amazon Web Services
-----------------

* [AWS Certified Solutions Architect – Professional](https://www.credly.com/badges/3f30bd86-6157-4c5d-9ff5-5b47f38cdb07/public_url) – Amazon Web Services (AWS), February 2023 – February 2025

Google Cloud

* [Generative AI Leader](https://www.cloudskillsboost.google/public_profiles/7f9c8f2d-8b1a-4e1a-9e1a-1a1a1a1a1a1a/certifications/generative-ai-leader) – Google Cloud, issued January 2023
* [Introduction to Generative AI](https://cdn.qwiklabs.com//TntrCzBhpKkF9LHUgvevvIKQb2+ufpupa1zPSlY/cs=) – Google Cloud, issued June 2023

Red Hat
--------

* Certificate of Expertise in Platform-as-a-Service
* Certificate of Expertise in Data Virtualization
* Certificate of Expertise in Clustering and Storage Management

VMware

* VMware Certified Professional – Data Center Virtualization
* VMware Sales Professional, Application Modernization, Data Management, Business Continuity, Virtualization of Business Critical Applications, Management, Cloud IaaS, Desktop Virtualization
* VMware Technical Sales Professional - Business Continuity, Virtualization of Business Critical Applications, Data Management, Infrastructure Virtualization
* VMware Certified Associate – Data Center Virtualization, Cloud, Workforce Mobility

VCE
---

* Certified to architect, deploy, and implement Vblock 100/200/300/700 infrastructure systems

Awards and Recognition
---------------------

* **Red Hat Innovation Award** – Honored for contributions to the FICO Analytic Cloud, which achieved a 70% faster time-to-market and generated over US $10 million in new revenue.

Volunteer Experience
--------------------

* **Humane Society** – Engaged as a volunteer animal caregiver, coordinating weekend adoption events and providing training for new volunteers.

Publications
------------

Title

Publisher

URL

A Year Later: Pros and Cons of the Dell and EMC Merger

TechTarget SearchStorage

[https://www.techtarget.com/searchstorage/feature/A-year-later-Pros-and-cons-of-the-Dell-and-EMC-merger](https://www.techtarget.com/searchstorage/feature/A-year-later-Pros-and-cons-of-the-Dell-and-EMC-merger)

FICO Chooses Red Hat to Deploy OpenStack, Management, and Storage Solutions for Agile Cloud Infrastructure

Red Hat Press Release

[https://www.redhat.com/en/about/press-releases/fico-chooses-red-hat-deploy-openstack-management-and-storage-solutions-agile-cloud-infrastructure](https://www.redhat.com/en/about/press-releases/fico-chooses-red-hat-deploy-openstack-management-and-storage-solutions-agile-cloud-infrastructure)

Microsoft, Red Hat Collaborate to Put Linux on Azure

RCP Magazine

[https://rcpmag.com/articles/2015/11/04/microsoft-red-hat-put-linux-on-azure.aspx](https://rcpmag.com/articles/2015/11/04/microsoft-red-hat-put-linux-on-azure.aspx)

Will Open Source Storage Make the Hyper Scale Dream Real?

The Register

[https://www.theregister.com/2015/11/09/open_source_hyperscale_storage/](https://www.theregister.com/2015/11/09/open_source_hyperscale_storage/)

FICO Embraces OpenStack

SiliconANGLE

[https://www.youtube.com/watch?v=NxOXdeOrfJE](https://www.youtube.com/watch?v=NxOXdeOrfJE)

FICO Proves the Mainstream OpenStack Adoption Point

Forbes

[https://www.forbes.com/sites/benkepes/2015/06/09/fico-proves-the-mainstream-openstack-adoption-point/](https://www.forbes.com/sites/benkepes/2015/06/09/fico-proves-the-mainstream-openstack-adoption-point/)

FICO Says OpenStack Enterprise Is Ready for Primetime

TechTarget SearchCloudComputing

[https://www.techtarget.com/searchcloudcomputing/tip/Five-challenges-with-open-source-cloud-infrastructure-tools](https://www.techtarget.com/searchcloudcomputing/tip/Five-challenges-with-open-source-cloud-infrastructure-tools)

How Storage Works in Containers

OpenStack Superuser

[https://superuser.openinfra.org/articles/how-storage-works-in-containers/](https://superuser.openinfra.org/articles/how-storage-works-in-containers/)

How to Build a Large-Scale Multi-Tenant Cloud Solution

LinkedIn

[https://www.linkedin.com/pulse/how-build-large-scale-multi-tenant-cloud-solution-gerasimatos](https://www.linkedin.com/pulse/how-build-large-scale-multi-tenant-cloud-solution-gerasimatos)

Think FICO Is a Credit Scoring Company? Nope: It’s About Large-Scale Analytics

OpenStack Superuser

[https://superuser.openinfra.org/articles/think-fico-is-a-credit-scoring-company-nope-it-s-about-large-scale-analytics/](https://superuser.openinfra.org/articles/think-fico-is-a-credit-scoring-company-nope-it-s-about-large-scale-analytics/)

Technical Skills
----------------

**Cloud Platforms & Infrastructure:** Amazon Web Services (AWS), Microsoft Azure, Google Cloud Platform (GCP), Red Hat OpenShift, SUSE Rancher, VMware, OpenStack, Amazon Elastic Kubernetes Service (EKS), Google Kubernetes Engine (GKE), IBM Cloud, VMware vSphere, Hybrid Cloud Architecture, Multi-Cloud Strategy, Private Cloud, Infrastructure as a Service (IaaS), Platform as a Service (PaaS), AWS Lambda, AWS Elastic Container Service (ECS), AWS Fargate, AWS Simple Storage Service (S3), AWS Relational Database Service (RDS), AWS Elastic Block Store (EBS), AWS Elastic File System (EFS), AWS Identity and Access Management (IAM), AWS Virtual Private Cloud (VPC), AWS Direct Connect, AWS Transit Gateway, Azure Functions, Azure Kubernetes Service, Azure Blob Storage, Azure SQL, Google Cloud Functions, Cloud Run, Cloud SQL

**Containers & DevOps:** Kubernetes, Docker, Helm, Istio, Linkerd, K3s, Rancher (RKE/RKE2), Terraform, Pulumi, Crossplane, Ansible, GitHub Actions, Jenkins, Tekton, Argo CD, Flux, GitOps, DevSecOps, Kustomize, Pods, Operators, Continuous Integration/Continuous Delivery (CI/CD), GitLab CI/CD, CircleCI, Travis CI, Spinnaker, AWS CloudFormation, Azure Resource Manager, Configuration Management, Infrastructure as Code (IaC)

**Observability & Security:** Prometheus, Grafana, ELK Stack (Elasticsearch, Logstash, Kibana), OpenTelemetry, OPA/Gatekeeper, HashiCorp Vault, Aqua Security, Falco, NeuVector, Jaeger, New Relic, Datadog, Splunk, Dynatrace, Loki, Thanos, Monitoring, Alerting, Log Management, Distributed Tracing, Metrics Collection, Performance Monitoring, Zero Trust Architecture, Mutual Transport Layer Security (mTLS), SPIFFE/SPIRE, Keycloak, OAuth2, OpenID Connect, Istio Security, Trivy, Cloud Security, Identity and Access Management, Security Frameworks, Compliance, Data Protection, Regulatory Standards
**Data & AI/ML:** Hadoop, Spark, Kafka, MLflow, Kubeflow, Seldon Core, Ray, SageMaker, Bedrock, Vertex AI, Machine Learning, Artificial Intelligence, Large Language Models (LLMs), TensorFlow, PyTorch, Keras, scikit-learn, Hugging Face, LangChain, Deep Learning, Neural Networks, Computer Vision, Natural Language Processing (NLP), Generative AI, Model Serving, MLOps, Data Analytics, Internet of Things (IoT) Solutions, Edge Computing, Blockchain Technology, Data Architecture, Data Mesh, Data Fabric, Data Lake, Data Warehouse, Data Lakehouse, Extract, Transform, Load/Extract, Load, Transform (ETL/ELT), Presto, Stream Processing, Batch Processing, Real-time Analytics, Time-series Data, Graph Databases, Vector Databases, Data Governance, Master Data Management

**Development & Architecture:** Python, Go, Java, JavaScript, Node.js, React, TypeScript, Bash, Envoy, NGINX, OpenYurt, EdgeX Foundry, Serverless, Multi-Cloud, Microservices Architecture, Application Programming Interface (API) Design & Development, Representational State Transfer (REST), gRPC, GraphQL, Event-Driven Architecture, Serverless Computing, Database Design, System Integration, Distributed Systems, Service Mesh, API Gateway, Service Discovery, Circuit Breaker, Command Query Responsibility Segregation (CQRS), Event Sourcing, Saga Pattern, WebSockets

**Leadership & Architecture:** Enterprise & Solution Architecture, Zero-Trust Security, FinOps, Agile/Scrum, Stakeholder Engagement, Technical Evangelism, Technical Leadership, Digital Transformation, Kanban, Scaled Agile Framework (SAFe), DevOps Culture, Site Reliability Engineering (SRE), Team Leadership, Project Management, Customer Success, Partner Enablement, Training and Development, Mentoring, Coaching, Technical Writing, Public Speaking, Workshop Facilitation, Community Building, Open Source Contribution, Thought Leadership, Business Development, Pre-sales Engineering

Languages
---------

* English (Native)
* Greek (Conversational)
* German (Conversational)
* Dutch (Conversational)

References
---------

Available upon request.