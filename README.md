# Resume Cloud Canvas

A modern, responsive React resume website with dynamic markdown-based content management and multi-format download capabilities.

## 🌟 Features

### **Dynamic Content Management**
- **Markdown-to-Website Integration**: Resume content is dynamically loaded from `updatedresume.md`
- **Real-time Updates**: Simply edit the markdown file to update website content
- **Intelligent Parsing**: Advanced markdown parser extracts structured data for professional presentation
- **Fallback System**: Graceful error handling with fallback data ensures site reliability

### **Multi-Format Downloads**
- **JSON Export**: Complete structured resume data in JSON format
- **Generated Markdown**: Clean, formatted markdown generated from parsed data
- **Original Markdown**: Download the source `updatedresume.md` file
- **Smart Filenames**: Automatically generated filenames based on resume data

### **Modern Web Experience**
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Interactive Navigation**: Tabbed interface for Experience, Skills, Projects, and Publications
- **Loading States**: Smooth loading indicators and error handling
- **Professional Styling**: Clean, modern design with Tailwind CSS

### **Developer-Friendly Architecture**
- **TypeScript**: Full type safety and excellent developer experience
- **Component-Based**: Modular React components for easy maintenance
- **Caching System**: 5-minute data caching for optimal performance
- **Hot Module Replacement**: Instant development feedback with Vite

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Git

### Installation

```bash
# Clone the repository
git clone https://github.com/nicholasg-dev/resume-cloud-canvas.git
cd resume-cloud-canvas

# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:8080`

### Updating Resume Content

1. **Edit the markdown file**: Modify `public/updatedresume.md` with your resume content
2. **Refresh the website**: Changes will be reflected automatically
3. **Verify sections**: Ensure all sections (Work Experience, Skills, Publications) display correctly

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # shadcn/ui components
│   └── ResumeDownload.tsx # Download functionality
├── hooks/               # Custom React hooks
│   └── useMarkdownResume.ts # Data loading and caching
├── pages/               # Page components
│   ├── Index.tsx        # Main resume page
│   ├── HeroSection.tsx  # Hero/header section
│   ├── ExperienceSection.tsx # Work experience
│   ├── SkillsSection.tsx # Technical skills
│   └── PublicationsSection.tsx # Publications & awards
├── services/            # Business logic
│   └── markdownDataService.ts # Markdown parsing
├── utils/               # Utility functions
│   └── markdownParser.ts # Core parsing logic
└── types/               # TypeScript type definitions

public/
└── updatedresume.md     # Resume content source
```

## 🛠 Technology Stack

### **Core Technologies**
- **React 18**: Modern React with hooks and functional components
- **TypeScript**: Type-safe development with excellent IDE support
- **Vite**: Fast build tool with hot module replacement
- **Tailwind CSS**: Utility-first CSS framework for rapid styling

### **UI Components**
- **shadcn/ui**: High-quality, accessible UI components
- **Radix UI**: Unstyled, accessible UI primitives
- **Lucide React**: Beautiful, customizable icons

### **Markdown Processing**
- **marked**: Fast markdown parser and compiler
- **gray-matter**: Front matter parser for markdown files

### **Development Tools**
- **ESLint**: Code linting and quality enforcement
- **Prettier**: Code formatting
- **TypeScript ESLint**: TypeScript-specific linting rules

## 📝 Content Management

### Markdown File Structure

The `updatedresume.md` file should follow this structure:

```markdown
# Your Name

Principal – Your Title | Your Specialization

Professional Summary
-----------------
Your professional summary here...

Work Experience
--------------
### Company Name — Position Title
Date Range

* **Achievement 1**: Description
* **Achievement 2**: Description

**Skills:** Technology 1 · Technology 2 · Technology 3

Technical Skills
---------------
**Category 1:** Skill 1, Skill 2, Skill 3
**Category 2:** Skill 1, Skill 2, Skill 3

Publications
-----------
| Title | Publisher | URL |
|-------|-----------|-----|
| Article Title | Publisher Name | [Link](url) |

Certifications
-------------
* [Certification Name](url) – Issuer, issued Date
```

### Supported Sections

- **Professional Summary**: Brief overview of your expertise
- **Work Experience**: Companies, positions, achievements, and technologies
- **Technical Skills**: Categorized skill sets
- **Publications**: Articles, papers, and media mentions
- **Certifications**: Professional certifications with links
- **Awards**: Recognition and achievements

## 🔧 Development

### Available Scripts

```bash
# Development server with hot reload
npm run dev

# Production build
npm run build

# Development build (with source maps)
npm run build:dev

# Preview production build
npm run preview

# Lint code
npm run lint

# Format code
npm run format
```

### Adding New Features

1. **Components**: Add new components in `src/components/`
2. **Pages**: Create new pages in `src/pages/`
3. **Hooks**: Add custom hooks in `src/hooks/`
4. **Services**: Add business logic in `src/services/`
5. **Types**: Define TypeScript types in `src/types/`

### Customization

#### Styling
- Modify `tailwind.config.ts` for theme customization
- Update component styles using Tailwind classes
- Customize shadcn/ui components in `src/components/ui/`

#### Data Structure
- Extend the `ResumeData` interface in `src/services/markdownDataService.ts`
- Update the markdown parser in `src/utils/markdownParser.ts`
- Modify components to handle new data fields

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

### Deployment Options

#### Netlify (Recommended)
1. Connect your GitHub repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Deploy automatically on git push

#### Vercel
1. Import project from GitHub
2. Vercel will auto-detect Vite configuration
3. Deploy with zero configuration

#### Static Hosting
Upload the `dist/` folder contents to any static hosting service:
- GitHub Pages
- AWS S3 + CloudFront
- Firebase Hosting
- Surge.sh

## 🔍 Testing Downloads

### JSON Download
- Click "Download Resume" → "JSON"
- Verify the downloaded file contains complete structured data
- Check filename format: `Your_Name_Resume.json`

### Markdown Downloads
- **Generated**: Click "Markdown (Generated)" for formatted output
- **Original**: Click "Markdown (Original)" for source file
- Verify content accuracy and formatting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🆘 Support

For questions, issues, or contributions:
- Open an issue on GitHub
- Check existing documentation
- Review the codebase for examples

---

**Built with ❤️ using modern web technologies**
