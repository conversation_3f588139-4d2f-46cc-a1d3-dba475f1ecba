[build]
  command = "npm install && npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "20"
  NPM_VERSION = "10"
  VITE_APP_VERSION = "$npm_package_version"
  NODE_OPTIONS = "--max-old-space-size=4096"

# Handle client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[dev]
  command = "npm run dev"
  port = 5173  # Vite's default port
  targetPort = 5173
  publish = "dist"
  autoLaunch = false
