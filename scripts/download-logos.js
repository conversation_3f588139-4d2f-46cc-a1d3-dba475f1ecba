import { createWriteStream, existsSync, mkdirSync, unlink } from "fs";
import { get } from "https";
import { fileURLToPath } from "url";
import { dirname, join } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Ensure logos directory exists
const logosDir = join(__dirname, "../public/logos");
if (!existsSync(logosDir)) {
  mkdirSync(logosDir, { recursive: true });
}

// Company logos with their URLs
const companies = [
  {
    name: "ahead",
    url: "https://www.thinkahead.com/wp-content/uploads/2023/09/AHEAD-logo-color.svg",
  },
  {
    name: "amazonwebservices",
    url: "https://upload.wikimedia.org/wikipedia/commons/9/93/Amazon_Web_Services_Logo.svg",
  },
  {
    name: "redhat",
    url: "https://upload.wikimedia.org/wikipedia/commons/e/e8/Red_Hat_logo.svg",
  },
  {
    name: "fico",
    url: "https://upload.wikimedia.org/wikipedia/commons/a/ab/FICO_Logo.svg",
  },
  {
    name: "americanexpress",
    url: "https://upload.wikimedia.org/wikipedia/commons/thumb/3/30/American_Express_logo.svg/1200px-American_Express_logo.svg.png",
  },
];

// Function to download a file
function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    const file = createWriteStream(filePath);
    get(url, (response) => {
      response.pipe(file);
      file.on("finish", () => {
        file.close();
        console.log(`Downloaded ${url} to ${filePath}`);
        resolve(filePath);
      });
    }).on("error", (error) => {
      unlink(filePath, () => {}); // Delete the file async
      console.error(`Error downloading ${url}:`, error.message);
      reject(error);
    });
  });
}

// Download all logos
async function downloadAllLogos() {
  for (const company of companies) {
    const extension = company.url.split(".").pop()?.split("?")[0] || "svg";
    const fileName = `${company.name}.${extension}`;
    const filePath = join(logosDir, fileName);

    try {
      await downloadFile(company.url, filePath);
    } catch (error) {
      console.error(`Failed to download ${company.name} logo:`, error.message);
    }
  }
  console.log("Logo download process completed!");
}

downloadAllLogos();
