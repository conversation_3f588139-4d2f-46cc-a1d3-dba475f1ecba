import { createWriteStream, existsSync, mkdirSync, unlink } from "fs";
import { get } from "https";
import { fileURLToPath } from "url";
import { dirname, join } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Ensure logos directory exists
const logosDir = join(__dirname, "../public/logos");
if (!existsSync(logosDir)) {
  mkdirSync(logosDir, { recursive: true });
}

// Company logos with their URLs from reliable sources
const companies = [
  {
    name: "redhat",
    url: "https://www.redhat.com/cms/managed-files/logo-red-hat-1-1.svg",
    extension: "svg",
  },
  {
    name: "fico",
    url: "https://www.fico.com/sites/default/files/2022-09/FICO-Logo-Color-RGB.svg",
    extension: "svg",
  },
  {
    name: "americanexpress",
    url: "https://www.americanexpress.com/content/dam/amex/us/merchant/supply/logo-download-center/american-express-logo/amex-logo-1.png",
    extension: "png",
  },
];

// Function to download a file
function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    const file = createWriteStream(filePath);
    get(url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        file.on("finish", () => {
          file.close();
          console.log(`Downloaded ${url} to ${filePath}`);
          resolve(filePath);
        });
      } else {
        reject(
          new Error(`Failed to download ${url}: Status ${response.statusCode}`),
        );
      }
    }).on("error", (error) => {
      unlink(filePath, () => {});
      reject(error);
    });
  });
}

// Download all logos
async function downloadAllLogos() {
  for (const company of companies) {
    const fileName = `${company.name}.${company.extension}`;
    const filePath = join(logosDir, fileName);

    try {
      await downloadFile(company.url, filePath);
    } catch (error) {
      console.error(`Failed to download ${company.name} logo:`, error.message);
    }
  }
  console.log("Logo download process completed!");
}

downloadAllLogos();
